import time
import random
from device_utils import get_device, get_device_name, connect_device
from ui_automation_utils import find_and_click



def single_swipe_zfb(d,direction="random"):
    """
    从屏幕中心附近开始，按指定方向快速滑动一次（适中距离+随机值）
    
    :param direction: 滑动方向，可选 "up"(向上)、"down"(向下) 或 "random"(随机，默认)
    """

    # 获取设备屏幕尺寸
    screen_info = d.info
    screen_width = screen_info['displayWidth']
    screen_height = screen_info['displayHeight']
    width, height = screen_width, screen_height
    
    # 计算中心点坐标
    center_x = width // 2
    center_y = height // 2
    
    # 设置滑动参数
    x_radius = 30  # X轴随机偏移范围
    base_distance = 500  # 基础滑动距离
    random_offset = 150  # 随机偏移范围
    
    # 生成起始点（中心点附近）
    start_x = random.randint(center_x - x_radius, center_x + x_radius)
    start_y = random.randint(center_y - 50, center_y + 50)  # 在中心点上下50像素内
    
    # 计算实际滑动距离（基础距离±随机值）
    actual_distance = base_distance + random.randint(-random_offset, random_offset)
    
    # 根据参数决定滑动方向
    if direction.lower() == "up":
        end_y = start_y - actual_distance
    elif direction.lower() == "down":
        end_y = start_y + actual_distance
    elif direction.lower() == "random":
        end_y = start_y + actual_distance if random.choice([True, False]) else start_y - actual_distance
    else:
        raise ValueError("direction 参数必须是 'up'、'down' 或 'random'")
    
    # 确保坐标不超出屏幕范围
    start_x = max(0, min(start_x, width - 1))
    start_y = max(0, min(start_y, height - 1))
    end_x = start_x  # 保持X轴不变
    end_y = max(100, min(end_y, height - 100))  # 保留上下100像素的边界
    
    # 执行滑动操作（duration参数控制滑动速度，越小越快）
    d.swipe(start_x, start_y, end_x, end_y, duration=0.2)
    
    direction_str = "向上" if end_y < start_y else "向下"
    print(f"单次{direction_str}滑动完成，距离：{abs(start_y - end_y)}像素")


def run_swipe_loop(d):
    """循环执行滑动操作：在10个视频范围内随机上下滑动，100次后不断上滑20次加载新视频"""
    max_videos = 10  # 最大加载视频数
    current_position = 0  # 当前位置（0-9）
    swipe_count = 0
    is_guanggao = False
    max_normal_swipes = 100  # 正常滑动的最大次数
    refresh_swipes = 20  # 刷新时连续上滑的次数
    not_found_count = 0  # 连续未找到红包金额的次数
    max_not_found = 5  # 最大连续未找到次数

    while True:
        try:
            # 检查是否需要进入刷新模式（每100次滑动后执行一次）
            if swipe_count > 0 and swipe_count % max_normal_swipes == 0:
                print(f"\n=== 已完成 {swipe_count} 次滑动，开始连续上滑 {refresh_swipes} 次加载新视频 ===")
                # 连续上滑20次来加载新视频
                for i in range(refresh_swipes):
                    single_swipe_zfb(d, direction="up")
                    print(f"刷新上滑 {i+1}/{refresh_swipes} 次")
                    # 刷新时等待时间稍短
                    time.sleep(random.uniform(1, 2))

                # 重置位置到顶部
                current_position = 0
                print("=== 刷新完成，继续正常滑动 ===\n")
                continue

            # 正常滑动逻辑（始终在10个视频范围内随机滑动）
            # 随机决定滑动方向（但在边界位置时强制反向）
            if current_position <= 0:
                direction = "down"  # 已经在顶部，只能向下
            elif current_position >= max_videos - 1:
                direction = "up"    # 已经在底部，只能向上
            else:
                direction = random.choice(["up", "down"])

            # 更新当前位置
            if direction == "down":
                current_position = min(current_position + 1, max_videos - 1)
            else:
                current_position = max(current_position - 1, 0)

            # 执行滑动并判断是否结束
            found_tomorrow = find_and_click("明日可领", click=False)
            found_tomorrow_again = find_and_click("明天再来", click=False) if found_tomorrow is None else None

            if found_tomorrow is None and found_tomorrow_again is None:
                # 两种情况都未找到，执行滑动
                single_swipe_zfb(d, direction=direction)
                swipe_count += 1
            # elif found_tomorrow_again is not None:
            #     # 找到"明天再来"的情况
            #     print("找到明天再来，返回")
            #     d.press("back")
            #     time.sleep(1)

            #     if find_and_click("去看看") is not None:
            #         is_guanggao = True
            #         single_swipe_zfb(d, direction=direction)
            #         swipe_count += 1
            #     else:
            #         print("没有找到去看看，结束了")
            #         break
            # else:
            #     # 找到"明日可领"的情况
            #     find_and_click("明日可领")

            #     find_and_click("去预约")

            #     if find_and_click("去看看") is not None:
            #         is_guanggao = True
            #         single_swipe_zfb(d, direction=direction)
            #         swipe_count += 1
            #     else:
            #         print("没有找到去看看，结束了")
            #         break
            else:
                print("结束了")
                break

            # 如果找不到红包金额，可能是进入了直播间，退出一下
            action_element = find_and_click("//*[@resource-id='com.alipay.android.living.dynamic:id/actionTv']", match_type='xpath', click=False)
            if action_element is None:
                not_found_count += 1
                print(f"可能进入了直播间，退出一下（连续未找到次数：{not_found_count}/{max_not_found}）")

                # 检查是否连续未找到超过限制
                if not_found_count >= max_not_found:
                    print(f"连续{max_not_found}次未找到红包金额，界面可能异常，停止运行")
                    break

                d.press("back")
                time.sleep(1)
            else:
                # 找到了红包金额，重置计数
                if not_found_count > 0:
                    print(f"找到红包金额，重置未找到计数（之前连续未找到{not_found_count}次）")
                    not_found_count = 0

            # 打印当前滑动统计
            print(f"\n已执行 {swipe_count} 次滑动（当前方向：{direction}）")
            print(f"当前位置：{current_position + 1}/{max_videos}")

            # 计算距离下次刷新的次数
            next_refresh_at = ((swipe_count // max_normal_swipes) + 1) * max_normal_swipes
            remaining_to_refresh = next_refresh_at - swipe_count
            print(f"距离下次刷新还有：{remaining_to_refresh} 次（每{max_normal_swipes}次刷新一次）")

            if is_guanggao:
                # 随机等待15 - 20秒（带0.1秒精度）
                wait_time = round(random.uniform(15, 20), 1)
                print(f"等待 {wait_time} 秒后继续...")
                time.sleep(wait_time)
            else:
                # 随机等待5 - 10秒（带0.1秒精度）
                wait_time = round(random.uniform(5, 10), 1)
                print(f"等待 {wait_time} 秒后继续...")
                time.sleep(wait_time)

        except Exception as e:
            print(f"执行出错: {str(e)}")
            time.sleep(5)  # 出错后等待5秒再重试

def 视频任务():

    d = get_device()

    run_swipe_loop(d)

if __name__ == "__main__":
    # 如果直接运行此文件，先连接设备再执行视频任务
    # 你可以根据需要修改设备配置
    device_config = {
        "sb_name": "k80pro",
        "sb_url": "**************:36123",
        "sb_url_params": None
    }

    try:
        connect_device(**device_config)
        print("设备连接成功，开始执行支付宝视频任务...")
        视频任务()
    except Exception as e:
        print(f"执行失败: {str(e)}")
